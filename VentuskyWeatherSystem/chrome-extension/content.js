// Ventusky Weather Extractor - Content Script (时间轴修复版)
// 专门修复时间轴切换和索引计算问题

class VentuskyWeatherExtractor {
    constructor() {
        this.isExtracting = false;
        this.currentDateIndex = 0;
        this.allWeatherData = {};
        this.config = null;
        this.targetHours = [2, 5, 8, 11, 17, 23];
        
        this.setupMessageListener();
        this.injectStyles();
        
        console.log('🌤️ Ventusky Weather Extractor Content Script 已加载 (时间轴修复版)');
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('📨 Content script收到消息:', message);
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    injectStyles() {
        if (document.getElementById('weather-extractor-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'weather-extractor-styles';
        style.textContent = `
            .weather-extractor-overlay {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                background: rgba(0, 0, 0, 0.9) !important;
                color: white !important;
                padding: 20px !important;
                border-radius: 12px !important;
                z-index: 999999 !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                max-width: 320px !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
            }
            .weather-extractor-progress {
                margin-top: 15px !important;
                background: rgba(255, 255, 255, 0.1) !important;
                height: 6px !important;
                border-radius: 3px !important;
                overflow: hidden !important;
            }
            .weather-extractor-progress-bar {
                background: #4CAF50 !important;
                height: 100% !important;
                transition: width 0.4s ease !important;
            }
        `;
        document.head.appendChild(style);
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'getCurrentCoordinates':
                    const coords = this.getCurrentCoordinates();
                    sendResponse(coords);
                    break;
                    
                case 'startExtraction':
                    if (this.isExtracting) {
                        sendResponse({ success: false, error: '提取正在进行中' });
                        return;
                    }
                    const result = await this.startExtraction(message.config);
                    sendResponse(result);
                    break;
                    
                case 'stopExtraction':
                    this.stopExtraction();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('❌ Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    getCurrentCoordinates() {
        try {
            const url = new URL(window.location.href);
            const params = url.searchParams.get('p');
            
            if (params) {
                const coords = params.split(';');
                if (coords.length >= 2) {
                    const lat = parseFloat(coords[0]);
                    const lon = parseFloat(coords[1]);
                    if (!isNaN(lat) && !isNaN(lon)) {
                        console.log('✅ 从URL获取坐标:', lat, lon);
                        return { success: true, lat, lon };
                    }
                }
            }
            
            console.log('⚠️ 无法从URL获取坐标');
            return { success: false, error: '无法从当前页面获取坐标，请手动输入' };
            
        } catch (error) {
            console.error('❌ 获取坐标失败:', error);
            return { success: false, error: error.message };
        }
    }

    async startExtraction(config) {
        console.log('🚀 开始天气数据提取 (时间轴修复版):', config);
        
        this.config = config;
        this.targetHours = config.selectedHours;
        this.isExtracting = true;
        this.currentDateIndex = 0;
        this.allWeatherData = {};

        try {
            this.showOverlay('🌤️ 初始化提取器...');
            
            if (!window.location.href.includes('ventusky.com')) {
                throw new Error('请在Ventusky页面使用此插件');
            }
            
            await this.navigateToCoordinates(config.lat, config.lon);
            await this.waitForPageLoad();
            
            this.updateOverlay('📊 开始提取数据...');
            
            // 提取每一天的数据
            for (let i = 0; i < config.dateList.length; i++) {
                if (!this.isExtracting) break;
                
                const date = config.dateList[i];
                this.currentDateIndex = i;
                
                this.updateOverlay(`📅 提取第${i + 1}/${config.dateList.length}天: ${date}`);
                console.log(`📊 进度: ${i+1}/${config.dateList.length} - ${date}`);
                
                try {
                    const dayData = await this.getSingleDayForecast(date);
                    if (dayData && Object.keys(dayData).length > 0) {
                        this.allWeatherData[date] = dayData;
                        console.log(`✅ ${date} 数据获取成功:`, dayData);
                    } else {
                        console.log(`❌ ${date} 数据获取失败`);
                    }
                    
                    // 安全的消息发送
                    this.sendMessageSafely({
                        action: 'updateProgress',
                        completed: i + 1
                    });
                    
                    await this.sleep(2000);
                    
                } catch (error) {
                    console.error(`❌ 获取 ${date} 数据时发生错误:`, error);
                    continue;
                }
            }
            
            // 整合最终结果
            const finalResults = {
                location: {
                    coordinates: this.formatCoordinates(config.lat, config.lon),
                    lat: config.lat,
                    lon: config.lon
                },
                date_range: {
                    start: config.startDate,
                    end: config.endDate,
                    total_days: config.dateList.length
                },
                forecast_hours: this.targetHours,
                weather_data: this.allWeatherData,
                extraction_time: new Date().toISOString(),
                data_source: 'Ventusky Chrome Extension - 时间轴修复版'
            };
            
            console.log('🎉 提取完成，最终结果:', finalResults);
            
            this.hideOverlay();
            
            // 安全的消息发送
            this.sendMessageSafely({
                action: 'extractionComplete',
                results: finalResults
            });
            
            return { success: true };
            
        } catch (error) {
            console.error('❌ 提取过程出错:', error);
            this.hideOverlay();
            
            this.sendMessageSafely({
                action: 'extractionError',
                error: error.message
            });
            
            return { success: false, error: error.message };
        } finally {
            this.isExtracting = false;
        }
    }

    sendMessageSafely(message) {
        try {
            if (chrome.runtime && chrome.runtime.sendMessage) {
                chrome.runtime.sendMessage(message);
            }
        } catch (error) {
            console.log('⚠️ 无法发送消息:', error.message);
        }
    }

    formatCoordinates(lat, lon) {
        const latDeg = Math.floor(lat);
        const latMin = Math.floor((lat - latDeg) * 60);
        const lonDeg = Math.floor(lon);
        const lonMin = Math.floor((lon - lonDeg) * 60);
        return `${latDeg}°${latMin}' ${lonDeg}°${lonMin}'`;
    }

    stopExtraction() {
        console.log('⏹️ 停止提取');
        this.isExtracting = false;
        this.hideOverlay();
    }

    async navigateToCoordinates(lat, lon) {
        const targetUrl = `https://www.ventusky.com/?p=${lat};${lon};11&l=temperature-2m`;
        console.log(`🎯 导航到位置: ${this.formatCoordinates(lat, lon)}`);
        console.log('目标URL:', targetUrl);
        
        if (window.location.href !== targetUrl) {
            console.log('🔄 重定向到目标坐标...');
            window.location.href = targetUrl;
            
            return new Promise((resolve) => {
                const checkLoad = () => {
                    if (document.readyState === 'complete') {
                        resolve();
                    } else {
                        setTimeout(checkLoad, 500);
                    }
                };
                setTimeout(checkLoad, 1000);
            });
        }
    }

    async waitForPageLoad() {
        console.log('⏳ 等待页面加载...');
        await this.waitForElement('canvas');
        await this.sleep(10000);
        console.log('✓ 页面加载完成');
    }

    async getSingleDayForecast(date) {
        console.log(`\n📅 获取${date}天气数据...`);

        try {
            if (this.currentDateIndex === 0) {
                console.log('📅 第一天，跳过日期选择器点击');
            } else {
                if (!await this.navigateToNextDate()) {
                    return null;
                }
            }

            if (!await this.resetToTemperatureLayer()) {
                return null;
            }
            
            const dayData = {};
            
            // 获取温度数据
            console.log('  🌡️ 温度...', '');
            const tempData = await this.getDataForAllHours('温度');
            console.log(` ✅ ${Object.keys(tempData).length}/6`);
            
            // 获取降水量数据
            console.log('  🌧️ 降水量...', '');
            await this.switchToDataType('降水量');
            const precipData = await this.getDataForAllHours('降水量');
            console.log(` ✅ ${Object.keys(precipData).length}/6`);
            
            // 获取云量数据
            console.log('  ☁️ 云量...', '');
            await this.switchToDataType('云量');
            const cloudData = await this.getDataForAllHours('云量');
            console.log(` ✅ ${Object.keys(cloudData).length}/6`);
            
            // 获取风速数据
            console.log('  💨 风速...', '');
            await this.switchToDataType('风速');
            const windData = await this.getDataForAllHours('风速');
            console.log(` ✅ ${Object.keys(windData).length}/6`);
            
            // 整合单日数据
            for (const hour of this.targetHours) {
                const timeKey = `${hour.toString().padStart(2, '0')}:00`;
                
                const temp = tempData[timeKey]?.temperature || 'N/A';
                const precip = precipData[timeKey]?.precipitation || 'N/A';
                const cloud = cloudData[timeKey]?.cloudcover || 'N/A';
                const wind = windData[timeKey]?.windspeed || 'N/A';
                
                let weatherCondition = 'N/A';
                if (temp !== 'N/A' && precip !== 'N/A' && cloud !== 'N/A' && wind !== 'N/A') {
                    weatherCondition = this.analyzeWeatherCondition(temp, precip, cloud, wind);
                } else {
                    weatherCondition = '数据不完整';
                }
                
                dayData[timeKey] = {
                    temperature: temp,
                    precipitation: precip,
                    cloudcover: cloud,
                    windspeed_100m: wind,
                    weather_condition: weatherCondition
                };
            }
            
            console.log(`✅ ${date}数据获取完成:`, dayData);
            return dayData;
            
        } catch (error) {
            console.error(`❌ 获取${date}数据失败:`, error);
            return null;
        }
    }

    analyzeWeatherCondition(temp, precip, cloud, wind) {
        try {
            const tempVal = parseInt(temp.match(/(\d+)/)[1]);
            const precipVal = parseFloat(precip.match(/(\d+(?:\.\d+)?)/)[1]);
            const cloudVal = parseInt(cloud.match(/(\d+)/)[1]);
            const windVal = parseInt(wind.match(/(\d+)/)[1]);
            
            let tempDesc, cloudDesc, precipDesc, windDesc, weatherIcon;
            
            if (tempVal <= 20) {
                tempDesc = "凉爽";
            } else if (tempVal <= 25) {
                tempDesc = "舒适";
            } else if (tempVal <= 30) {
                tempDesc = "温暖";
            } else {
                tempDesc = "炎热";
            }
            
            if (cloudVal <= 25) {
                cloudDesc = "晴朗";
            } else if (cloudVal <= 50) {
                cloudDesc = "少云";
            } else if (cloudVal <= 75) {
                cloudDesc = "多云";
            } else {
                cloudDesc = "阴天";
            }
            
            if (precipVal === 0) {
                precipDesc = "";
                weatherIcon = cloudVal <= 50 ? "☀️" : "☁️";
            } else if (precipVal < 2.5) {
                precipDesc = "小雨";
                weatherIcon = "🌦️";
            } else if (precipVal < 10) {
                precipDesc = "中雨";
                weatherIcon = "🌧️";
            } else {
                precipDesc = "大雨";
                weatherIcon = "⛈️";
            }
            
            if (windVal <= 15) {
                windDesc = "微风";
            } else if (windVal <= 25) {
                windDesc = "和风";
            } else {
                windDesc = "强风";
            }
            
            let condition;
            if (precipDesc) {
                condition = `${tempDesc}${cloudDesc}${precipDesc}${windDesc}`;
            } else {
                condition = `${tempDesc}${cloudDesc}${windDesc}`;
            }
            
            return `${weatherIcon} ${condition}`;
        } catch {
            return "数据解析失败";
        }
    }

    async navigateToNextDate() {
        console.log(`📅 前进到下一天（第${this.currentDateIndex + 1}天）...`);

        try {
            document.body.click();
            await this.sleep(2000);

            const event = new KeyboardEvent('keydown', {
                key: 'ArrowRight',
                code: 'ArrowRight',
                keyCode: 39,
                which: 39,
                bubbles: true
            });
            
            document.body.dispatchEvent(event);
            document.dispatchEvent(event);
            
            await this.sleep(5000);

            console.log(`✅ 已前进到第${this.currentDateIndex + 1}天`);
            return true;

        } catch (error) {
            console.log(`❌ 导航失败: ${error}`);
            return false;
        }
    }

    async resetToTemperatureLayer() {
        console.log('🔄 重置到温度图层...');
        
        try {
            const tempElements = document.querySelectorAll('*');
            
            for (const element of tempElements) {
                try {
                    const text = element.textContent || element.innerText || '';
                    if (text.includes('温度') && element.offsetParent !== null && element.offsetWidth > 0) {
                        element.click();
                        await this.sleep(3000);
                        console.log('✅ 已重置到温度图层');
                        return true;
                    }
                } catch {
                    continue;
                }
            }
            
            for (const element of tempElements) {
                try {
                    const text = element.textContent || element.innerText || '';
                    if ((text.includes('Temperature') || text.includes('temperature')) && 
                        element.offsetParent !== null && element.offsetWidth > 0) {
                        element.click();
                        await this.sleep(3000);
                        console.log('✅ 已重置到温度图层');
                        return true;
                    }
                } catch {
                    continue;
                }
            }
            
            console.log('⚠️ 未找到温度选项，假设已在温度图层');
            return true;
            
        } catch (error) {
            console.log(`❌ 重置到温度图层失败: ${error}`);
            return false;
        }
    }

    async switchToDataType(dataType) {
        console.log(`🔄 切换到${dataType}图层...`);
        
        if (dataType === "温度") {
            return true;
        }
        
        const typeMapping = {
            "降水量": ["降水量", "precipitation", "降水"],
            "云量": ["云量", "cloud", "云"],
            "风速": ["风速", "wind", "风"]
        };
        
        const keywords = typeMapping[dataType] || [dataType];
        
        for (const keyword of keywords) {
            try {
                const elements = document.querySelectorAll('*');
                for (const element of elements) {
                    const text = element.textContent || element.innerText || '';
                    if (text.includes(keyword) && element.offsetParent !== null && element.offsetWidth > 0) {
                        console.log(`🎯 找到${dataType}选项:`, element, text);
                        element.click();
                        await this.sleep(3000);
                        
                        if (dataType === "风速") {
                            console.log('🏔️ 开始选择100米高度...');
                            await this.select100mHeight();
                        }
                        
                        console.log(`✅ 成功切换到${dataType}图层`);
                        return true;
                    }
                }
            } catch {
                continue;
            }
        }
        
        console.log(`⚠️ 未找到${dataType}选项`);
        return false;
    }

    async select100mHeight() {
        console.log('🏔️ 选择100米高度...');
        
        try {
            await this.sleep(2000);
            
            // 更精确的100米高度选择逻辑
            const allElements = document.querySelectorAll('*');
            console.log('🔍 查找高度选择元素...');
            
            // 优先查找明确的"地上100米"链接
            for (const element of allElements) {
                try {
                    const text = element.textContent || element.innerText || '';
                    if (text.trim() === '地上100米' && element.tagName === 'A' && element.offsetParent !== null) {
                        console.log(`🎯 找到精确的地上100米链接: "${text.trim()}"`);
                        element.click();
                        await this.sleep(2000);
                        console.log('✅ 成功选择地上100米');
                        return true;
                    }
                } catch {
                    continue;
                }
            }
            
            // 如果没找到精确的，查找包含"100米"的元素
            for (const element of allElements) {
                try {
                    const text = element.textContent || element.innerText || '';
                    if (text.includes('地上100米') && element.offsetParent !== null && element.offsetWidth > 0) {
                        console.log(`🎯 找到包含地上100米的元素: "${text.trim()}"`);
                        element.click();
                        await this.sleep(2000);
                        console.log('✅ 选择了包含100米的选项');
                        return true;
                    }
                } catch {
                    continue;
                }
            }
            
            console.log('⚠️ 未找到100米高度选项，使用默认高度');
            return false;
            
        } catch (error) {
            console.log(`❌ 选择100米高度失败: ${error}`);
            return false;
        }
    }

    async findTimelineElements() {
        console.log('🔍 查找时间轴元素...');
        
        const timelineElements = [];
        const allLinks = document.querySelectorAll('a');
        
        console.log(`🔍 检查${allLinks.length}个链接元素`);
        
        // 精确模拟Python脚本的逻辑：只查找真正的时间轴元素
        for (const link of allLinks) {
            try {
                if (link.offsetParent !== null) {
                    const rect = link.getBoundingClientRect();
                    const location = { x: rect.left, y: rect.top };
                    const size = { width: rect.width, height: rect.height };
                    const text = link.textContent || '';
                    
                    // 精确模拟Python脚本的条件：
                    // 1. 位置：y > 800 (页面底部)
                    // 2. 尺寸：width 30-80px, height 20-50px
                    // 3. 内容：必须是时间格式 (XX:XX)
                    if (location.y > 800 && 
                        size.width > 30 && size.width < 80 && 
                        size.height > 20 && size.height < 50 &&
                        /^\d{2}:\d{2}$/.test(text.trim())) { // 只接受 XX:XX 格式
                        
                        timelineElements.push({
                            element: link,
                            x_position: location.x,
                            text: text.trim(),
                            hour: parseInt(text.trim().split(':')[0]) // 提取小时数
                        });
                        
                        console.log('✅ 确认时间轴元素:', {
                            text: text.trim(),
                            hour: parseInt(text.trim().split(':')[0]),
                            x_position: location.x,
                            y: location.y,
                            width: size.width,
                            height: size.height
                        });
                    }
                }
            } catch {
                continue;
            }
        }
        
        // 按x坐标排序 - 精确模拟Python: sort(key=lambda x: x['x_position'])
        timelineElements.sort((a, b) => a.x_position - b.x_position);
        
        console.log(`✅ 找到${timelineElements.length}个真正的时间轴元素`);
        
        // 打印排序后的时间轴元素
        console.log('📋 时间轴元素列表（按x坐标排序）:');
        timelineElements.forEach((item, index) => {
            console.log(`  ${index}: ${item.text} (${item.hour}点) - x: ${item.x_position}`);
        });
        
        return timelineElements;
    }

    async getDataForAllHours(dataType) {
        console.log(`📊 获取${dataType}的所有时间点数据`);
        
        const timelineElements = await this.findTimelineElements();
        if (!timelineElements.length) {
            console.log('❌ 未找到时间轴元素');
            return {};
        }
        
        const dataResults = {};
        
        console.log(`📊 开始遍历${this.targetHours.length}个目标时间点`);
        
        for (let i = 0; i < this.targetHours.length; i++) {
            const targetHour = this.targetHours[i];
            
            try {
                console.log(`🕐 处理${targetHour}点数据 (${i+1}/${this.targetHours.length})`);
                
                // 修复的关键逻辑：直接查找匹配的小时
                let targetElement = null;
                let elementIndex = -1;
                
                // 方法1：直接查找匹配的小时
                for (let j = 0; j < timelineElements.length; j++) {
                    if (timelineElements[j].hour === targetHour) {
                        targetElement = timelineElements[j];
                        elementIndex = j;
                        break;
                    }
                }
                
                // 方法2：如果没找到精确匹配，查找最接近的
                if (!targetElement) {
                    let minDiff = 24;
                    for (let j = 0; j < timelineElements.length; j++) {
                        const diff = Math.abs(timelineElements[j].hour - targetHour);
                        if (diff < minDiff) {
                            minDiff = diff;
                            targetElement = timelineElements[j];
                            elementIndex = j;
                        }
                    }
                }
                
                if (!targetElement) {
                    console.log(`⚠️ 未找到${targetHour}点对应的时间轴元素`);
                    continue;
                }
                
                const element = targetElement.element;
                
                console.log(`🎯 点击时间轴元素 ${elementIndex}/${timelineElements.length-1}:`, {
                    text: targetElement.text,
                    targetHour: targetHour,
                    actualHour: targetElement.hour,
                    x_position: targetElement.x_position
                });
                
                // 滚动到元素并点击
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await this.sleep(1000);
                
                element.click();
                await this.sleep(3000); // 增加等待时间确保页面更新
                
                // 悬停获取数据
                await this.hoverMapCenter();
                
                // 提取数据
                let hourData = null;
                if (dataType === "温度") {
                    hourData = this.extractTemperatureData();
                } else if (dataType === "降水量") {
                    hourData = this.extractPrecipitationData();
                } else if (dataType === "云量") {
                    hourData = this.extractCloudcoverData();
                } else if (dataType === "风速") {
                    hourData = this.extractWindspeedData();
                }
                
                if (hourData) {
                    const timeKey = `${targetHour.toString().padStart(2, '0')}:00`;
                    dataResults[timeKey] = hourData;
                    console.log(`✅ ${timeKey} ${dataType}数据:`, hourData);
                } else {
                    console.log(`⚠️ ${targetHour}点${dataType}数据提取失败`);
                }
                
            } catch (error) {
                console.error(`❌ 获取${targetHour}点数据失败:`, error);
                continue;
            }
        }
        
        console.log(`📊 ${dataType}数据提取完成，成功获取${Object.keys(dataResults).length}/${this.targetHours.length}个时间点:`, dataResults);
        return dataResults;
    }

    async hoverMapCenter() {
        console.log('🎯 悬停在地图中心');
        
        try {
            const canvas = document.querySelector('canvas');
            if (!canvas) {
                console.log('❌ 未找到canvas元素');
                return;
            }
            
            const rect = canvas.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            console.log(`🎯 地图中心坐标: (${centerX}, ${centerY})`);
            
            // 创建多种鼠标事件确保触发
            const events = ['mouseover', 'mouseenter', 'mousemove'];
            
            for (const eventType of events) {
                const event = new MouseEvent(eventType, {
                    clientX: rect.left + centerX,
                    clientY: rect.top + centerY,
                    bubbles: true,
                    cancelable: true
                });
                canvas.dispatchEvent(event);
            }
            
            await this.sleep(3000); // 增加等待时间确保数据显示
            
        } catch (error) {
            console.error('❌ 悬停地图中心失败:', error);
        }
    }

    extractTemperatureData() {
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            if (element.offsetParent !== null) {
                const text = element.textContent || element.innerText || '';
                const tempMatch = text.match(/(-?\d+)\s*°C?/);
                if (tempMatch) {
                    const tempValue = parseInt(tempMatch[1]);
                    if (tempValue >= -50 && tempValue <= 60) {
                        console.log(`🌡️ 找到温度数据: ${tempValue}°C`);
                        return { temperature: tempValue + '°C' };
                    }
                }
            }
        }
        console.log('⚠️ 未找到温度数据');
        return null;
    }

    extractPrecipitationData() {
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            if (element.offsetParent !== null) {
                const text = element.textContent || element.innerText || '';
                
                const precipMatches = [
                    text.match(/(\d+[。.]\d+)\s*mm/),
                    text.match(/(\d+)\s*mm/)
                ];
                
                for (const precipMatch of precipMatches) {
                    if (precipMatch) {
                        const precipText = precipMatch[1];
                        const precipValue = parseFloat(precipText.replace('。', '.'));
                        if (precipValue >= 0 && precipValue <= 50) {
                            console.log(`🌧️ 找到降水量数据: ${precipValue} mm`);
                            return { precipitation: precipValue + ' mm' };
                        }
                    }
                }
            }
        }
        console.log('⚠️ 未找到降水量数据，返回0mm');
        return { precipitation: '0 mm' };
    }

    extractCloudcoverData() {
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            if (element.offsetParent !== null) {
                const text = element.textContent || element.innerText || '';
                const cloudMatch = text.match(/(\d+)\s*%/);
                if (cloudMatch) {
                    const cloudValue = parseInt(cloudMatch[1]);
                    if (cloudValue >= 0 && cloudValue <= 100) {
                        console.log(`☁️ 找到云量数据: ${cloudValue}%`);
                        return { cloudcover: cloudValue + '%' };
                    }
                }
            }
        }
        console.log('⚠️ 未找到云量数据，返回0%');
        return { cloudcover: '0%' };
    }

    extractWindspeedData() {
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            if (element.offsetParent !== null) {
                const text = element.textContent || element.innerText || '';
                const windMatch = text.match(/(\d+(?:\.\d+)?)\s*km\/h/i);
                if (windMatch) {
                    const windValue = parseFloat(windMatch[1]);
                    if (windValue >= 0 && windValue <= 200) {
                        console.log(`💨 找到风速数据: ${windValue} km/h`);
                        return { windspeed: windValue + ' km/h' };
                    }
                }
            }
        }
        console.log('⚠️ 未找到风速数据，返回0 km/h');
        return { windspeed: '0 km/h' };
    }

    // 工具函数
    async waitForElement(selector, timeout = 15000) {
        console.log(`⏳ 等待元素: ${selector}`);
        
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`✅ 元素已存在: ${selector}`);
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver(() => {
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    console.log(`✅ 元素已加载: ${selector}`);
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未找到`));
            }, timeout);
        });
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showOverlay(message) {
        this.hideOverlay();
        
        const overlay = document.createElement('div');
        overlay.className = 'weather-extractor-overlay';
        overlay.id = 'weather-extractor-overlay';
        overlay.innerHTML = `
            <div><strong>🌤️ 天气数据提取器</strong></div>
            <div style="margin-top: 8px;">${message}</div>
            <div class="weather-extractor-progress">
                <div class="weather-extractor-progress-bar" id="weather-extractor-progress-bar" style="width: 0%"></div>
            </div>
        `;
        
        document.body.appendChild(overlay);
    }

    updateOverlay(message) {
        const overlay = document.getElementById('weather-extractor-overlay');
        if (overlay) {
            const messageDiv = overlay.children[1];
            if (messageDiv) {
                messageDiv.textContent = message;
            }
            
            // 更新进度条
            const progressBar = document.getElementById('weather-extractor-progress-bar');
            if (progressBar && this.config) {
                const progress = ((this.currentDateIndex + 1) / this.config.dateList.length) * 100;
                progressBar.style.width = `${progress}%`;
            }
        }
    }

    hideOverlay() {
        const overlay = document.getElementById('weather-extractor-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
}

// 等待页面加载后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new VentuskyWeatherExtractor();
    });
} else {
    new VentuskyWeatherExtractor();
}
