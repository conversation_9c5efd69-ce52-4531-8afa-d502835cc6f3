<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>6c998d3e-11ad-4b4e-881a-be9e6fe4ae24</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>main.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>VentuskyWeatherSystem</Name>
    <RootNamespace>VentuskyWeatherSystem</RootNamespace>
    <InterpreterId>MSBuild|venv|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="main.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="ventusky_complete_weather_excel.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="ventusky_multi_day_forecast_fixed.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="legacy\ventusky_july26_cloudcover.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="legacy\ventusky_july26_precipitation_fixed.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="legacy\ventusky_july26_windspeed_final.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="legacy\ventusky_final_success_scraper.py">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="ARCHITECTURE.md" />
    <Content Include="chrome-extension\content-simple-fix.js" />
    <Content Include="chrome-extension\content-windspeed-fixed.js" />
    <Content Include="chrome-extension\manifest.json" />
    <Content Include="chrome-extension\manifest-v2.json" />
    <Content Include="chrome-extension\popup.html" />
    <Content Include="chrome-extension\options.html" />
    <Content Include="chrome-extension\QUICK_START.md" />
    <Content Include="chrome-extension\README.md" />
    <Content Include="chrome-extension\COMPLETE_FIX_GUIDE.md" />
    <Content Include="chrome-extension\INSTALLATION_GUIDE.md" />
    <Content Include="chrome-extension\TIMELINE_FIX_README.md" />
    <Content Include="chrome-extension\TROUBLESHOOTING.md" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="chrome-extension\content.js" />
    <Content Include="chrome-extension\content-fixed-v2.js" />
    <Content Include="chrome-extension\content-debug.js" />
    <Content Include="chrome-extension\background.js" />
    <Content Include="chrome-extension\popup.js" />
    <Content Include="chrome-extension\options.js" />
    <Content Include="chrome-extension\test-timeline.js" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="chrome-extension\content.css" />
    <Content Include="chrome-extension\popup.css" />
    <Content Include="chrome-extension\options.css" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="chrome-extension\WINDSPEED_100M_FIX.md" />
    <Content Include="PROJECT_OVERVIEW.md" />
    <Content Include="requirements.txt" />
    <Content Include="README.md" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="data\" />
    <Folder Include="output\" />
    <Folder Include="scripts\" />
    <Folder Include="legacy\" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="venv\">
      <Id>venv</Id>
      <Version>3.9</Version>
      <Description>venv (Python 3.9)</Description>
      <InterpreterPath>venv\Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>venv\Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>
