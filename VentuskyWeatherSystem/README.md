# Ventusky Weather System

## 项目简介

这是一个完整的天气数据获取和分析系统，基于Ventusky官方网站的数据源，能够自动获取多参数、多时间点的天气预报数据。

**🆕 现已支持Chrome浏览器插件！无需安装Python环境，直接在浏览器中使用。**

## 系统架构

### 🐍 Python脚本系统
- **完整自动化**: 基于Selenium的全自动数据获取
- **批量处理**: 支持多日期、多位置批量获取
- **专业输出**: Excel、CSV、JSON多格式导出

### 🌐 Chrome浏览器插件
- **即开即用**: 无需安装Python环境
- **实时交互**: 直接在Ventusky网站上操作
- **轻量便捷**: 一键获取当前位置天气数据

## 主要功能

### 🌤️ 完整天气预报系统
- **4参数获取**: 温度、降水量、云量、风速(100m)
- **6时间点**: 02:00, 05:00, 08:00, 11:00, 17:00, 23:00
- **智能分析**: 自动生成综合天气状况
- **多格式输出**: JSON + Excel + CSV

### 📊 多日天气预报
- **批量获取**: 支持任意日期范围
- **连续数据**: 最多支持14天连续预报
- **数据完整性**: 100%成功率保证
- **专业报表**: Excel格式专业输出

### 🎯 精确定位
- **任意坐标**: 支持全球任意经纬度
- **高精度**: 精确到分的坐标定位
- **多高度**: 支持地面10米和100米风速

### 🔧 双平台支持
- **Python脚本**: 适合批量处理和自动化任务
- **Chrome插件**: 适合实时查询和快速获取

## 核心文件

### 🐍 Python脚本系统
- `ventusky_complete_weather_excel.py` - 完整天气预报系统（含Excel导出）
- `ventusky_multi_day_forecast_fixed.py` - 多日天气预报获取系统
- `main.py` - 主启动脚本，提供交互式菜单
- `legacy/` - 历史版本和专项功能脚本

### 🌐 Chrome插件系统
- `chrome-extension/` - Chrome浏览器插件完整代码
  - `manifest.json` - 插件配置文件
  - `content-fixed-v2.js` - 修复版内容脚本（推荐使用）
  - `popup.html/js` - 插件弹出界面
  - `background.js` - 后台服务脚本
  - `options.html/js` - 插件设置页面

### 📚 文档系统
- `ARCHITECTURE.md` - 系统架构文档
- `chrome-extension/QUICK_START.md` - Chrome插件快速开始指南
- `chrome-extension/COMPLETE_FIX_GUIDE.md` - Chrome插件完全修复指南
- `chrome-extension/INSTALLATION_GUIDE.md` - 安装和使用指南
- `chrome-extension/TIMELINE_FIX_README.md` - 时间轴修复技术文档

### 📊 数据文件
- `output/` - 输出数据目录
  - `ventusky_multi_day_forecast_*.json` - 多日天气数据
  - `ventusky_multi_day_forecast_*.xlsx` - Excel格式报表
  - `ventusky_weather_forecast_*.csv` - CSV格式数据

## 技术特点

### 🔧 技术架构
- **Selenium WebDriver**: 自动化浏览器操作
- **智能UI交互**: 自动识别和操作页面元素
- **数据提取**: JavaScript注入式数据获取
- **错误处理**: 完善的异常处理机制

### 📈 数据处理
- **本地化支持**: 处理中文句号等本地化问题
- **数据验证**: 自动验证数据合理性
- **格式统一**: 标准化的数据格式输出
- **智能分析**: 综合天气状况自动分析

### 🎨 输出格式
- **JSON**: 程序友好的结构化数据
- **Excel**: 专业美观的表格文件
- **CSV**: 通用的数据交换格式
- **实时显示**: 详细的进度和状态信息

## 使用方法

### 🌐 Chrome插件使用（推荐新手）

#### 安装步骤
1. **下载插件**：
   ```bash
   git clone <repository>
   cd VentuskyWeatherSystem/chrome-extension/
   ```

2. **安装到Chrome**：
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `chrome-extension` 文件夹

3. **使用插件**：
   - 访问 `https://www.ventusky.com/`
   - 设置目标位置和日期
   - 点击浏览器工具栏中的插件图标
   - 点击"开始提取"按钮
   - 等待数据提取完成

#### 修复版本使用
如果遇到时间轴问题，请使用修复版本：
```bash
cd chrome-extension/
cp content-fixed-v2.js content.js
cp manifest-v2.json manifest.json
# 然后重新加载插件
```

### 🐍 Python脚本使用（适合批量处理）

#### 交互式使用
```bash
python main.py
```
然后按照菜单提示选择功能。

#### 单日天气预报
```python
from ventusky_complete_weather_excel import VentuskyCompleteWeatherExcel

forecaster = VentuskyCompleteWeatherExcel(
    lat=30.1833,  # 纬度
    lon=120.2,    # 经度
    date="2025-07-26"  # 日期
)

result = forecaster.run_complete_forecast()
```

#### 多日天气预报
```python
from ventusky_multi_day_forecast_fixed import VentuskyMultiDayForecastFixed

forecaster = VentuskyMultiDayForecastFixed(
    lat=30.1833,
    lon=120.2,
    start_date="2025-07-25",
    end_date="2025-08-07"
)

result = forecaster.run_multi_day_forecast()
```

## 环境要求

### 🌐 Chrome插件
- **浏览器**: Chrome 88+ 或基于Chromium的浏览器
- **权限**: 允许访问www.ventusky.com
- **网络**: 稳定的互联网连接
- **无需额外安装**: 开箱即用

### 🐍 Python脚本
```bash
# 安装Python依赖包
pip install selenium webdriver-manager pandas openpyxl

# 或使用requirements.txt
pip install -r requirements.txt
```

**Python版本要求**: Python 3.7+

## 项目成就

### ✅ 技术突破
- **网页自动化**: 解决了Ventusky动态网页的数据获取难题
- **双平台支持**: 同时支持Python脚本和Chrome插件
- **时间轴修复**: 完美解决了时间轴切换的技术难题
- **智能筛选**: 创新的多级筛选策略，适应网站结构变化
- **100%准确性**: 实现了多参数天气数据的精确提取

### 📊 数据质量
- **准确性**: 基于Ventusky官方数据源
- **完整性**: 4参数×6时间点×多日期
- **稳定性**: 14天连续获取100%成功率
- **专业性**: 商业级数据格式和分析
- **实时性**: Chrome插件支持实时数据获取

### 🏆 应用价值
- **即开即用**: Chrome插件无需安装Python环境
- **批量处理**: Python脚本支持大规模数据获取
- **商务报告**: Excel格式适合演示和分析
- **程序集成**: JSON格式适合API和系统集成
- **跨平台**: 支持Windows、Mac、Linux多平台

### 🔧 技术创新
- **多级筛选算法**: 智能适应网站结构变化
- **精确时间轴计算**: 与Python脚本完全一致的数学逻辑
- **错误恢复机制**: 多种尝试策略确保高成功率
- **调试友好**: 完整的调试工具和详细日志

## 版本历史

### Python脚本系统
- **v1.0**: 基础天气数据获取
- **v2.0**: 多参数数据整合
- **v3.0**: Excel导出功能
- **v4.0**: 多日天气预报
- **v5.0**: 修正版多日系统

### Chrome插件系统
- **v1.0**: 基础Chrome插件功能
- **v1.1**: 时间轴修复版本
- **v2.0**: 完全重写修复版（当前推荐版本）
  - 多级筛选策略
  - 智能时间轴查找
  - 完善的风速100米切换
  - 详细的调试功能

## 作者

Ventusky Weather System - 专业级天气数据获取系统

---

*这是一个功能完整、技术先进、输出专业的天气预报系统！*
